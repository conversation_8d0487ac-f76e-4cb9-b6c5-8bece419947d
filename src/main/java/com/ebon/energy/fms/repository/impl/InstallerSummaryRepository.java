package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.UserVO;
import com.ebon.energy.fms.domain.vo.installer.FleetHealthSummaryViewModel;
import com.ebon.energy.fms.domain.vo.installer.InstallationSummaryViewModel;
import com.ebon.energy.fms.mapper.third.FleetHealthSummaryMapper;
import com.ebon.energy.fms.repository.IInstallerSummaryRepository;
import com.ebon.energy.fms.service.UserService;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

@Slf4j
@Repository
@RequiredArgsConstructor
public class InstallerSummaryRepository implements IInstallerSummaryRepository {

    private final FleetHealthSummaryMapper fleetHealthSummaryMapper;
    private final UserService userService;

    @Override
    public List<FleetHealthSummaryViewModel> readFleetHealthSummaryAsync(String installerId) {
        try {

            String filterByUserId = RequestUtil.getPortolUserId();

            // 处理模拟用户逻辑
            if (!StringUtils.isEmpty(installerId)) {
                // 简化版本：移除CanImpersonate检查，通过权限系统处理
                filterByUserId = installerId;
            }

            if (filterByUserId == null) {
                log.warn("Filter by user id is null");
                return List.of();
            }

            return fleetHealthSummaryMapper.getFleetHealthSummary(filterByUserId);

        } catch (Exception e) {
            log.error("获取舰队健康摘要失败，installerId: {}", installerId, e);
            throw new BizException(CommonErrorCodeEnum.ERROR, "获取舰队健康摘要失败: " + e.getMessage());
        }
    }

    @Override
    public List<InstallationSummaryViewModel> readInstallationSummaryAsync(String installerId) {
        String filterByUserId = RequestUtil.getPortolUserId();

        // 处理模拟用户逻辑
        if (!StringUtils.isEmpty(installerId)) {
            // 简化版本：移除CanImpersonate检查，通过权限系统处理
            filterByUserId = installerId;
        }

        if (filterByUserId == null) {
            log.warn("Filter by user id is null");
            return List.of();
        }

        return List.of();
    }

}
