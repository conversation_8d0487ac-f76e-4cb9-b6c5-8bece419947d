// TotalInstallationSummary.java
// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo.installer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;
import java.util.Optional;

@Data
public class TotalInstallationSummary {
    @JsonProperty("Installations")
    private List<TotalInstallationDetail> installations;

    public int getTotal() {
        return Optional.ofNullable(installations)
                .map(list -> list.stream().mapToInt(TotalInstallationDetail::getInstallationCount).sum())
                .orElse(0);
    }
}

